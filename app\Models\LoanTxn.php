<?php

namespace App\Models;

use App\Enums\Loan\LoanTxnStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * LoanTxn model for managing loan transactions
 */
class LoanTxn extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'loan_id',
        'loan_installment_id',
        'loan_txn_type_id',
        'txn_type',
        'amount',
        'status',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'loan_installment_id' => 'integer',
            'loan_txn_type_id' => 'integer',
            'amount' => 'decimal:2',
            'status' => LoanTxnStatus::class,
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan that owns this transaction.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the loan installment that owns this transaction.
     */
    public function loanInstallment(): BelongsTo
    {
        return $this->belongsTo(LoanInstallment::class);
    }

    /**
     * Get the loan transaction type that owns this transaction.
     */
    public function loanTxnType(): BelongsTo
    {
        return $this->belongsTo(LoanTxnType::class);
    }

    /**
     * Get the transaction details for this transaction.
     */
    public function loanTxnDetails(): HasMany
    {
        return $this->hasMany(LoanTxnDetail::class);
    }

    /**
     * Get the user who created this transaction.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this transaction.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted this transaction.
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate code if not provided
        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new loan transaction.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('LTX');
    }

    /**
     * Scope a query to get transactions for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'code', 'amount', 'status')
            ->orderBy('code');
    }

    public function scopeUnpaid($q)
    {
        return $q->where('status', 0);
    }

    public function scopeLateInterest($q)
    {
        return $q->where('loan_txn_type_id', 2);
    }

    public function scopeLegalFee($q)
    {
        return $q->where('loan_txn_type_id', 3);
    }

    public function scopePostage($q)
    {
        return $q->where('loan_txn_type_id', 5);
    }

    public function scopeMiscCharge($q)
    {
        return $q->where('loan_txn_type_id', 4);
    }

    public function scopeInstallment($q)
    {
        return $q->where('loan_txn_type_id', 1);
    }

    public function repaymentQueue(Request $request): LengthAwarePaginator
    {
        $queries = [
            LoanTxn::unpaid()->lateInterest()->where('loan_id', $this->id)->orderBy('tenure'),
            LoanTxn::unpaid()->legalFee()->where('loan_id', $this->id)->orderBy('created_at'),
            LoanTxn::unpaid()->postage()->where('loan_id', $this->id)->orderBy('tenure'),
            LoanTxn::unpaid()->miscCharge()->where('loan_id', $this->id)->orderBy('created_at'),
            LoanTxn::unpaid()->installment()->where('loan_id', $this->id)->orderBy('tenure'),
        ];

        $merged = collect();
        foreach ($queries as $query) {
            $merged = $merged->merge($query->get());
        }

        $withMeta = $merged->map(function ($txn) {
            return [
                'id' => $txn->id,
                'type' => $txn->txn_type,
                'amount' => $txn->amount,
                'tenure' => optional($txn->installment)->tenure,
                'date' => $txn->created_at,
                'model' => $txn,
            ];
        });

        $page = LengthAwarePaginator::resolveCurrentPage();
        $perPage = 10;
        $results = $withMeta->forPage($page, $perPage)->values();

        return new LengthAwarePaginator($results, $withMeta->count(), $perPage, $page, [
            'path' => $request->url(),
            'query' => $request->query(),
        ]);
    }
}
